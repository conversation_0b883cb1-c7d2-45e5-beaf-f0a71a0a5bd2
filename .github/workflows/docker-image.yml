name: Docker Image CI/CD

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  publish-docker:
    runs-on: ubuntu-latest

    steps:
      - name: Pull The Codes
        uses: actions/checkout@v5
        with:
          fetch-depth: 0  # Must be 0 so we can fetch tags

      - name: Get latest tag (only on manual trigger)
        id: get-latest-tag
        if: github.event_name == 'workflow_dispatch'
        run: |
          tag=$(git describe --tags --abbrev=0)
          echo "latest_tag=$tag" >> $GITHUB_OUTPUT

      - name: Checkout to latest tag (only on manual trigger)
        if: github.event_name == 'workflow_dispatch'
        run: git checkout ${{ steps.get-latest-tag.outputs.latest_tag }}

      - name: Set QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: Soulter
          password: ${{ secrets.GHCR_GITHUB_TOKEN }}

      - name: Build and Push Docker to DockerHub and Github GHCR
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/astrbot:latest
            ${{ secrets.DOCKER_HUB_USERNAME }}/astrbot:${{ github.event_name == 'workflow_dispatch' && steps.get-latest-tag.outputs.latest_tag || github.ref_name }}
            ghcr.io/soulter/astrbot:latest
            ghcr.io/soulter/astrbot:${{ github.event_name == 'workflow_dispatch' && steps.get-latest-tag.outputs.latest_tag || github.ref_name }}

      - name: Post build notifications
        run: echo "Docker image has been built and pushed successfully"
