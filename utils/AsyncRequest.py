import aiohttp

base_url = "https://api.csqaq.com/api/v1"

async def get(url: str, *, token: str, params: dict | None = None, timeout: float = 10.0):
    headers = {"ApiToken": token}
    timeout_cfg = aiohttp.ClientTimeout(total=timeout)
    async with aiohttp.ClientSession(timeout=timeout_cfg, headers=headers) as session:
        async with session.get(url, params=params) as resp:
            if resp.status not in (200, 201):
                raise Exception(f"HTTP {resp.status}: {await resp.text()}")
            if "application/json" in resp.headers.get("content-type", ""):
                return await resp.json()
            return await resp.text()


async def post(url: str, *, token: str, json: dict | None = None, data: dict | None = None, timeout: float = 10.0):
    headers = {"ApiToken": token}
    timeout_cfg = aiohttp.ClientTimeout(total=timeout)
    async with aiohttp.ClientSession(timeout=timeout_cfg, headers=headers) as session:
        async with session.post(url, json=json, data=data) as resp:
            if resp.status not in (200, 201):
                raise Exception(f"HTTP {resp.status}: {await resp.text()}")
            if "application/json" in resp.headers.get("content-type", ""):
                return await resp.json()
            return await resp.text()


__all__ = ["get", "post"]
