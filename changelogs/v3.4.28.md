# What's Changed

1. ✨ 新增: 管理面板支持搜索插件
2. ✨ 新增: 支持传递 OneBot 的 notice, request 事件类型，如戳一戳，进退群请求等
3. ✨ 新增: 插件支持自定义过滤算子 by @AraragiEro
4. ✨ 新增: 添加命令和命令组的别名支持 by @Cvandia
4. ✨ 新增: 提供了一个方法以删除分段回复后的某些字符，如末尾的标点符号。 by @Soulter and @Nothingness-Void
5. ⚡ 优化: 优化了分段回复和回复时at,引用都打开时的一些体验性问题
7. 🐛 修复: 分段回复导致了不完全的非 LLM 输出 #503
8. 🐛 修复: 添加 no_proxy 环境变量以支持本地请求, 修复在代理状态下时的 502 错误当通过 LMStudio, Ollama 本地部署 LLM 时 #504 #514
9. 💡🐛 修复: 修复转发消息的字数阈值功能 #510
10. 💡🐛 修复: 修复 Dify 下无法主动回复的问题 #494