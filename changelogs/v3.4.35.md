# What's Changed

1. ✨ 新增: 添加 GPT-SoVits-Inference(GSVI) TTS 支持 #545 #351 by @Fridemn
2. ✨ 新增: Telegram 支持发送文件和语音
3. ✨ 新增: 完善插件在禁用/重载时的逻辑，添加 terminate() Star 父类方法
4. ✨ 新增: 添加 AstrBot 启动完成时的事件钩子；添加获取指定平台适配器（Platform）的接口
5. ✨ 新增: 分离本地插件和插件市场
6. ⚡ 优化: 代码执行器使用指令 `/pi file` 来指定上传文件以更好适配全平台；
7. ⚡ 优化: 切换 Provider 时如果没有打开 Provider 开关，自动打开。
8. ⚡ 优化: 为 switch_conv 的 index 参数添加类型判断 by @Kx-Y
9.  ⚡ 优化: WebUI 缓存插件市场数据防止重复请求
10. ⚡ 优化: 插件市场搜索同时支持对插件描述进行搜索
11. ⚡ 优化: 将 Flask 初始化时允许的最大文件体积设置为 128 MB by @inori-3333
12. ⚡ 优化: 插件市场、更新项目的视觉反馈
13. ‼️‼️ 🐛 修复: 插件 AsyncGenerator 在没有执行 yield 语句的情况下设置事件结果无法被处理的问题
14. ‼️‼️ 🐛 修复: telegram @ 任何人都会触发机器人 #669
15. ‼‼️ 🐛 修复: wecom 加载失败的问题 #659
16. ‼‼️ 🐛 修复: gewechat 'TypeName' 解析错误 #680 #682
17. 🔧 Dev: 使用 ruff 格式化工程，添加了 pre-commit-ci