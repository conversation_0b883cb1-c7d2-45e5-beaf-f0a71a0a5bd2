# What's Changed
（Pre release）
1. 使用事件队列和事件总线解耦消息平台适配器与事件处理的逻辑；
2. 替换为采用装饰器的 handler 注册风格，支持通过消息事件类型、适配器类型、正则表达式、指令名开头、指令组注册 handler （指令或者监听器），不再推荐通过函数的 handler 注册方式。
3. 解耦了事件处理时的固定逻辑，采用流水线代替。
4. 解耦了 Provider 的相关处理逻辑。
5. 解耦了 Platform 相关处理逻辑。
6. aiocqhttp 适配器支持设置群聊白名单、私聊白名单；
7. aiocqhttp 适配器将图片转换成 base64 格式上报，而不需要先上传到图床；https://github.com/Soulter/AstrBot/issues/219
8. qq_official 适配器在群聊/ C2C 场景下以 base64 格式直接上传到 QQ 服务器，而不需要先上传到图床；
9. 移除了对 nakuru 适配器的支持；
10. 移除了 update, reboot 等指令；
11. 支持使用插件仓库镜像源安装插件、更新项目；
12. 支持接入微信
13. 移除了内嵌的管理面板构建文件。
14. 移除了 nakuru-project 库以适应 Pydantic V2（但仍然保留其对 OneBot 数据结构的封装文件），使用 OneBot 连接到 QQ 请使用 aiocqhttp 适配器（仅支持反向 Websockets，即 AstrBot 做 Websockets 服务器端）
15. 新的文档

> 不向后兼容配置文件。