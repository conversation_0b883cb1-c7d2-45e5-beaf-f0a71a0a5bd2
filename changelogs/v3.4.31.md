# What's Changed

> 提示：改动范围较大

1. ✨ 新增: 添加对 Anthropic Claude 的支持 by @Rt39
2. ✨ 新增: 支持阿里云百炼应用(dashscope)智能体、工作流 #552 by @Soulter
3. ✨ 新增: 支持 AstrBot 更新使用 Github 加速地址 by @Fridemn
4. ✨ 新增: 适配多节点的转发消息，添加新的消息段 `Nodes`
5. ✨ 新增: 支持在管理面板重启（设置页）
6. ✨ 新增: 前端支持以列表展示正式版和开发版的列表
7. ✨ 新增: 支持插件禁止默认的llm调用（event.should_call_llm()）#579
8. 🍺 重构: 支持更大范围的热重载以及管理面板将平台和提供商配置独立化 by @Soulter
9. ⚡ 优化: 启动时检查端口占用 by @Fridemn
10. ⚡ 优化: 添加控制台关闭自动滚动按钮 by @Fridemn
11. ⚡ 优化: 在聊天页面添加粘贴图片的快捷键提示 #557
12. 🐛 修复: 修复 webchat 未处理 base64 的问题 by @Raven95676
13. 🐛 修复: 修复 aiocqhttp_platform_adapter 文件相关判断逻辑 by @Raven95676
14. ‼️🐛 修复: 修复 gemini 请求时出现多次不支持函数工具调用最后 429 的问题