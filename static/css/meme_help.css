/* Meme Help Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    font-size: 18px;
    background: white;
}

.container {
    width: 100%;
    background: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0 auto;
}

.header {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    inset: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
}

.header h1, .header .subtitle, .header .created-by {
    position: relative;
    z-index: 1;
}

.header h1 {
    font-size: 2.8em;
    margin-bottom: 10px;
}

.header .subtitle {
    font-size: 1.3em;
    opacity: 0.9;
    margin-bottom: 15px;
}

.header .created-by {
    font-size: 1.1em;
    opacity: 0.85;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.15);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
    margin-top: 10px;
}

.content {
    padding: 50px 40px;
}

.section {
    margin-bottom: 45px;
}

.section-title {
    font-size: 1.5em;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 3px solid #3498db;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #3498db;
}

.command-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    margin-top: 25px;
}

.command-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.command-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(52, 152, 219, 0.05) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.command-card:hover::before {
    transform: translateX(100%);
}

.command-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-left-color: #e74c3c;
}

.command-name {
    font-size: 1.1em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.command-desc {
    color: #7f8c8d;
    line-height: 1.5;
    font-size: 0.95em;
}

.admin-section {
    margin-top: 45px;
}

.admin-section .command-card {
    border-left-color: #e74c3c;
}

.admin-section .command-card::before {
    background: linear-gradient(45deg, transparent 30%, rgba(231, 76, 60, 0.05) 50%, transparent 70%);
}

.usage-tips {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-top: 35px;
}

.usage-tips h3 {
    margin-bottom: 15px;
    font-size: 1.3em;
}

.usage-tips ul {
    list-style: none;
}

.usage-tips li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
    line-height: 1.4;
}

.usage-tips li::before {
    content: '💡';
    position: absolute;
    left: 0;
}



.emoji {
    font-size: 1.2em;
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .command-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    .content {
        padding: 40px 30px;
    }
}

@media (max-width: 768px) {
    .command-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    .content {
        padding: 30px 20px;
    }
    .header {
        padding: 30px 20px;
    }
    .header h1 {
        font-size: 2.2em;
    }
    .header .subtitle {
        font-size: 1.1em;
    }
    .section-title {
        font-size: 1.3em;
    }
    .command-card, .usage-tips {
        padding: 20px;
    }
    .usage-tips {
        margin-top: 25px;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 16px;
    }
    .header h1 {
        font-size: 1.8em;
    }
    .header .subtitle {
        font-size: 1em;
    }
    .content {
        padding: 20px 15px;
    }
    .command-card, .usage-tips {
        padding: 15px;
    }
}
