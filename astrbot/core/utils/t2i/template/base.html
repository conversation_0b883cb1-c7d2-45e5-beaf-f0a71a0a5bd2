<!doctype html>
<html>
<head>
  <meta charset="utf-8"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" integrity="sha384-wcIxkf4k558AjM3Yz3BBFQUbk/zgIYC2R0QpeeYb+TwlBVMrlgLqwRjRtGZiK7ww" crossorigin="anonymous">
  <link rel="stylesheet" href="/path/to/styles/default.min.css">
  <script src="/path/to/highlight.min.js"></script>
  <script>hljs.highlightAll();</script>
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js" integrity="sha384-hIoBPJpTUs74ddyc4bFZSM1TVlQDA60VBbJS0oA934VSz82sBx1X7kSx2ATBDIyd" crossorigin="anonymous"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"
      onload="renderMathInElement(document.getElementById('content'),{delimiters: [{left: '$$', right: '$$', display: true},{left: '$', right: '$', display: false}]});"></script>
</head>
<body>
  <div style="background-color: #3276dc; color: #fff; font-size: 64px; ">
    <span style="font-weight: bold; margin-left: 16px"># AstrBot</span>
    <span>{{ version }}</span>
  </div>
  <article style="margin-top: 32px" id="content"></article>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script>
    document.getElementById('content').innerHTML = marked.parse(`{{ text | safe}}`);
  </script>

</body>
</html>
<style>
    #content {
        min-width: 200px;
        max-width: 85%;
        margin: 0 auto;
        padding: 2rem 1em 1em;
      }
    
      body {
        word-break: break-word;
        line-height: 1.75;
        font-weight: 400;
        font-size: 32px;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        color: #333;
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
      }
      h1, h2, h3, h4, h5, h6 {
        line-height: 1.5;
        margin-top: 35px;
        margin-bottom: 10px;
        padding-bottom: 5px;
      }
      h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
        margin-top: -1.5rem;
        margin-bottom: 1rem;
      }
      h1::before, h2::before, h3::before, h4::before, h5::before, h6::before {
        content: "#";
        display: inline-block;
        color: #3eaf7c;
        padding-right: 0.23em;
      }
      h1 {
        position: relative;
        font-size: 2.5rem;
        margin-bottom: 5px;
      }
      h1::before {
        font-size: 2.5rem;
      }
      h2 {
        padding-bottom: 0.5rem;
        font-size: 2.2rem;
        border-bottom: 1px solid #ececec;
      }
      h3 {
        font-size: 1.5rem;
        padding-bottom: 0;
      }
      h4 {
        font-size: 1.25rem;
      }
      h5 {
        font-size: 1rem;
      }
      h6 {
        margin-top: 5px;
      }
      p {
        line-height: inherit;
        margin-top: 22px;
        margin-bottom: 22px;
      }
      strong {
        color: #3eaf7c;
      }
      img {
        max-width: 100%;
        border-radius: 2px;
        display: block;
        margin: auto;
        border: 3px solid rgba(62, 175, 124, 0.2);
      }
      hr {
        border-top: 1px solid #3eaf7c;
        border-bottom: none;
        border-left: none;
        border-right: none;
        margin-top: 32px;
        margin-bottom: 32px;
      }
      code {
        font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
        word-break: break-word;
        overflow-x: auto;
        padding: 0.2rem 0.5rem;
        margin: 0;
        color: #3eaf7c;
        font-size: 0.85em;
        background-color: rgba(27, 31, 35, 0.05);
        border-radius: 3px;
      }
      pre {
        font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
        overflow: auto;
        position: relative;
        line-height: 1.75;
        border-radius: 6px;
        border: 2px solid #3eaf7c;
      }
      pre > code {
        font-size: 12px;
        padding: 15px 12px;
        margin: 0;
        word-break: normal;
        display: block;
        overflow-x: auto;
        color: #333;
        background: #f8f8f8;
      }
      a {
        font-weight: 500;
        text-decoration: none;
        color: #3eaf7c;
      }
      a:hover, a:active {
        border-bottom: 1.5px solid #3eaf7c;
      }
      a:before {
        content: "⇲";
      }
      table {
        display: inline-block !important;
        font-size: 12px;
        width: auto;
        max-width: 100%;
        overflow: auto;
        border: solid 1px #3eaf7c;
      }
      thead {
        background: #3eaf7c;
        color: #fff;
        text-align: left;
      }
      tr:nth-child(2n) {
        background-color: rgba(62, 175, 124, 0.2);
      }
      th, td {
        padding: 12px 7px;
        line-height: 24px;
      }
      td {
        min-width: 120px;
      }
      blockquote {
        color: #666;
        padding: 1px 23px;
        margin: 22px 0;
        border-left: 0.5rem solid rgba(62, 175, 124, 0.6);
        border-color: #42b983;
        background-color: #f8f8f8;
      }
      blockquote::after {
        display: block;
        content: "";
      }
      blockquote > p {
        margin: 10px 0;
      }
      details {
        border: none;
        outline: none;
        border-left: 4px solid #3eaf7c;
        padding-left: 10px;
        margin-left: 4px;
      }
      details summary {
        cursor: pointer;
        border: none;
        outline: none;
        background: white;
        margin: 0px -17px;
      }
      details summary::-webkit-details-marker {
        color: #3eaf7c;
      }
      ol, ul {
        padding-left: 28px;
      }
      ol li, ul li {
        margin-bottom: 0;
        list-style: inherit;
      }
      ol li .task-list-item, ul li .task-list-item {
        list-style: none;
      }
      ol li .task-list-item ul, ul li .task-list-item ul, ol li .task-list-item ol, ul li .task-list-item ol {
        margin-top: 0;
      }
      ol ul, ul ul, ol ol, ul ol {
        margin-top: 3px;
      }
      ol li {
        padding-left: 6px;
      }
      ol li::marker {
        color: #3eaf7c;
      }
      ul li {
        list-style: none;
      }
      ul li:before {
        content: "•";
        margin-right: 4px;
        color: #3eaf7c;
      }
      @media (max-width: 720px) {
        h1 {
          font-size: 24px;
       }
        h2 {
          font-size: 20px;
       }
        h3 {
          font-size: 18px;
       }
      }

</style>